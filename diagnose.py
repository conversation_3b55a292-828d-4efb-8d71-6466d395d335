#!/usr/bin/env python3
"""
أداة تشخيص البوت - للتحقق من جميع المكونات
"""

import sys
import os
import importlib.util

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} - مدعوم")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} - غير مدعوم (يتطلب 3.8+)")
        return False

def check_module(module_name, description=""):
    """فحص وجود مكتبة"""
    try:
        spec = importlib.util.find_spec(module_name)
        if spec is not None:
            print(f"   ✅ {module_name} - متوفر {description}")
            return True
        else:
            print(f"   ❌ {module_name} - غير متوفر {description}")
            return False
    except Exception as e:
        print(f"   ❌ {module_name} - خطأ: {e}")
        return False

def check_required_modules():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات المطلوبة...")
    
    modules = [
        ("setuptools", "(أساسي للتثبيت)"),
        ("flask", "(خادم الويب)"),
        ("telegram", "(بوت Telegram)"),
        ("twilio", "(بوت WhatsApp)"),
        ("requests", "(طلبات HTTP)"),
        ("dotenv", "(متغيرات البيئة)")
    ]
    
    all_good = True
    for module, desc in modules:
        if not check_module(module, desc):
            all_good = False
    
    return all_good

def check_config_files():
    """فحص ملفات التكوين"""
    print("\n📄 فحص ملفات التكوين...")
    
    files = [
        ("requirements.txt", "متطلبات المشروع"),
        ("config.py", "إعدادات البوت"),
        (".env", "متغيرات البيئة"),
        ("setup.py", "إعداد التثبيت"),
        ("Dockerfile", "إعداد Docker")
    ]
    
    all_good = True
    for filename, desc in files:
        if os.path.exists(filename):
            print(f"   ✅ {filename} - موجود ({desc})")
        else:
            print(f"   ⚠️  {filename} - غير موجود ({desc})")
            if filename in ["config.py", "requirements.txt"]:
                all_good = False
    
    return all_good

def check_env_variables():
    """فحص متغيرات البيئة"""
    print("\n🔧 فحص متغيرات البيئة...")
    
    # تحميل متغيرات البيئة
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("   ✅ تم تحميل ملف .env")
    except:
        print("   ⚠️  لم يتم تحميل ملف .env")
    
    # فحص المتغيرات المهمة
    vars_to_check = [
        ("TELEGRAM_BOT_TOKEN", "توكن بوت Telegram"),
        ("TWILIO_ACCOUNT_SID", "معرف حساب Twilio"),
        ("STORE_NAME", "اسم المتجر")
    ]
    
    for var_name, desc in vars_to_check:
        value = os.getenv(var_name)
        if value:
            print(f"   ✅ {var_name} - مُعرف ({desc})")
        else:
            print(f"   ⚠️  {var_name} - غير مُعرف ({desc})")

def check_bot_components():
    """فحص مكونات البوت"""
    print("\n🤖 فحص مكونات البوت...")
    
    try:
        from utils.message_handler import MessageHandler
        handler = MessageHandler()
        print("   ✅ MessageHandler - يعمل")
        
        # اختبار معالجة رسالة
        test_response = handler.process_message("مرحبا")
        if test_response and len(test_response) > 0:
            print("   ✅ معالجة الرسائل - تعمل")
        else:
            print("   ❌ معالجة الرسائل - لا تعمل")
            
    except Exception as e:
        print(f"   ❌ MessageHandler - خطأ: {e}")

def check_network_connectivity():
    """فحص الاتصال بالشبكة"""
    print("\n🌐 فحص الاتصال بالشبكة...")
    
    try:
        import requests
        
        # فحص الاتصال بـ Telegram
        try:
            response = requests.get("https://api.telegram.org", timeout=5)
            if response.status_code == 200:
                print("   ✅ الاتصال بـ Telegram API - يعمل")
            else:
                print("   ⚠️  الاتصال بـ Telegram API - مشكلة")
        except:
            print("   ❌ الاتصال بـ Telegram API - فشل")
        
        # فحص الاتصال بـ Twilio
        try:
            response = requests.get("https://api.twilio.com", timeout=5)
            print("   ✅ الاتصال بـ Twilio API - يعمل")
        except:
            print("   ❌ الاتصال بـ Twilio API - فشل")
            
    except Exception as e:
        print(f"   ❌ فحص الشبكة - خطأ: {e}")

def main():
    """التشخيص الرئيسي"""
    print("🔍 بدء تشخيص البوت متعدد المنصات...")
    print("=" * 50)
    
    results = []
    
    # فحص Python
    results.append(check_python_version())
    
    # فحص المكتبات
    results.append(check_required_modules())
    
    # فحص الملفات
    results.append(check_config_files())
    
    # فحص متغيرات البيئة
    check_env_variables()
    
    # فحص مكونات البوت
    check_bot_components()
    
    # فحص الشبكة
    check_network_connectivity()
    
    # النتيجة النهائية
    print("\n" + "=" * 50)
    if all(results):
        print("🎉 التشخيص مكتمل - جميع المكونات الأساسية تعمل!")
        print("✅ البوت جاهز للتشغيل")
    else:
        print("⚠️  التشخيص مكتمل - هناك مشاكل تحتاج إصلاح")
        print("❌ يرجى إصلاح المشاكل المذكورة أعلاه")
    
    print("\n📋 للتشغيل:")
    print("   python run_fixed.py")
    print("   أو")
    print("   python main.py")

if __name__ == "__main__":
    main()
