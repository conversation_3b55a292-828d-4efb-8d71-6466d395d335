<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تجريب البوت - متجر الأناقة للملابس</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .chat-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(45deg, #25D366, #128C7E);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message.bot {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .message.user .message-bubble {
            background: #007bff;
            color: white;
            margin-left: 10px;
        }

        .message.bot .message-bubble {
            background: #e9ecef;
            color: #333;
            margin-right: 10px;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #dee2e6;
        }

        .quick-buttons {
            margin-bottom: 15px;
        }

        .quick-btn {
            margin: 5px;
            border-radius: 20px;
            font-size: 14px;
        }

        .typing-indicator {
            display: none;
            padding: 10px;
            font-style: italic;
            color: #6c757d;
        }

        .status-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
        }

        .status-online {
            background: #28a745;
            color: white;
        }

        .status-offline {
            background: #dc3545;
            color: white;
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <div class="chat-container">
            <div class="chat-header position-relative">
                <div class="status-indicator status-online" id="statusIndicator">
                    <i class="fas fa-circle"></i> متصل
                </div>
                <h2><i class="fab fa-whatsapp"></i> تجريب بوت متجر الأناقة</h2>
                <p class="mb-0">اختبر البوت مباشرة من هنا</p>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="message bot">
                    <div class="message-bubble">
                        <strong>🤖 بوت متجر الأناقة</strong><br>
                        مرحباً بك في متجر الأناقة للملابس! 👋<br><br>
                        يمكنني مساعدتك في:<br>
                        🛍️ عرض المنتجات<br>
                        📏 معرفة المقاسات<br>
                        🚚 معلومات التوصيل<br>
                        💰 الأسعار والعروض<br><br>
                        اكتب "منتجات" لعرض أحدث المنتجات
                    </div>
                </div>
            </div>

            <div class="typing-indicator" id="typingIndicator">
                <i class="fas fa-robot"></i> البوت يكتب...
            </div>

            <div class="chat-input">
                <div class="quick-buttons">
                    <button class="btn btn-outline-primary quick-btn" onclick="sendQuickMessage('مرحبا')">
                        <i class="fas fa-hand-wave"></i> مرحبا
                    </button>
                    <button class="btn btn-outline-success quick-btn" onclick="sendQuickMessage('منتجات')">
                        <i class="fas fa-shopping-bag"></i> المنتجات
                    </button>
                    <button class="btn btn-outline-info quick-btn" onclick="sendQuickMessage('توصيل')">
                        <i class="fas fa-truck"></i> التوصيل
                    </button>
                    <button class="btn btn-outline-warning quick-btn" onclick="sendQuickMessage('طلب 1')">
                        <i class="fas fa-cart-plus"></i> طلب منتج
                    </button>
                    <button class="btn btn-outline-secondary quick-btn" onclick="sendQuickMessage('مساعدة')">
                        <i class="fas fa-question-circle"></i> مساعدة
                    </button>
                </div>

                <div class="input-group">
                    <input type="text" class="form-control" id="messageInput" placeholder="اكتب رسالتك هنا..."
                        onkeypress="handleKeyPress(event)">
                    <button class="btn btn-primary" onclick="sendMessage()">
                        <i class="fas fa-paper-plane"></i> إرسال
                    </button>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fab fa-telegram"></i> Telegram Bot</h5>
                    </div>
                    <div class="card-body">
                        <p>للتجريب على Telegram:</p>
                        <code>@YourBotUsername</code>
                        <br><small class="text-muted">استبدل بالاسم الحقيقي للبوت</small>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fab fa-whatsapp"></i> WhatsApp Bot</h5>
                    </div>
                    <div class="card-body">
                        <p>للتجريب على WhatsApp:</p>
                        <code>****** 523 8886</code>
                        <br><small class="text-muted">رقم Twilio Sandbox</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/chat.js') }}"></script>
    <script>
        // كود إضافي للتوافق مع النسخة القديمة
        let isTyping = false;

        // إرسال رسالة سريعة
        function sendQuickMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }

        // التعامل مع الضغط على Enter
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // إرسال رسالة
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            // إضافة رسالة المستخدم
            addMessage(message, 'user');
            input.value = '';

            // إظهار مؤشر الكتابة
            showTyping();

            try {
                // إرسال الرسالة للبوت
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                if (response.ok) {
                    const data = await response.json();
                    setTimeout(() => {
                        hideTyping();
                        addMessage(data.response, 'bot');
                    }, 1000); // محاكاة وقت الاستجابة
                } else {
                    throw new Error('فشل في الاتصال بالخادم');
                }
            } catch (error) {
                setTimeout(() => {
                    hideTyping();
                    addMessage('عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'bot');
                    updateStatus(false);
                }, 1000);
            }
        }

        // إضافة رسالة للمحادثة
        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble';

            if (sender === 'bot') {
                bubbleDiv.innerHTML = `<strong>🤖 البوت:</strong><br>${text}`;
            } else {
                bubbleDiv.textContent = text;
            }

            messageDiv.appendChild(bubbleDiv);
            messagesContainer.appendChild(messageDiv);

            // التمرير للأسفل
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // إظهار مؤشر الكتابة
        function showTyping() {
            document.getElementById('typingIndicator').style.display = 'block';
            isTyping = true;
        }

        // إخفاء مؤشر الكتابة
        function hideTyping() {
            document.getElementById('typingIndicator').style.display = 'none';
            isTyping = false;
        }

        // تحديث حالة الاتصال
        function updateStatus(online) {
            const indicator = document.getElementById('statusIndicator');
            if (online) {
                indicator.className = 'status-indicator status-online';
                indicator.innerHTML = '<i class="fas fa-circle"></i> متصل';
            } else {
                indicator.className = 'status-indicator status-offline';
                indicator.innerHTML = '<i class="fas fa-circle"></i> غير متصل';
            }
        }

        // فحص حالة الخادم
        async function checkServerStatus() {
            try {
                const response = await fetch('/health');
                updateStatus(response.ok);
            } catch (error) {
                updateStatus(false);
            }
        }

        // فحص الحالة كل 30 ثانية
        setInterval(checkServerStatus, 30000);

        // فحص أولي
        checkServerStatus();
    </script>
</body>

</html>