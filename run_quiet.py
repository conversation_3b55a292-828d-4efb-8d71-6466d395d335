#!/usr/bin/env python3
"""
تشغيل البوت بدون تحذيرات التطوير
"""

import os
import sys
import logging
from werkzeug.serving import WSGIRequestHandler

# إخفاء تحذيرات Flask
import warnings
warnings.filterwarnings("ignore", message="This is a development server")

# إعداد السجلات
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)

# إخفاء سجلات werkzeug المزعجة
log = logging.getLogger('werkzeug')
log.setLevel(logging.ERROR)

def main():
    """تشغيل البوت بهدوء"""
    
    # إعداد متغيرات البيئة
    os.environ['STORE_NAME'] = 'متجر الأناقة للملابس'
    os.environ['CURRENCY'] = 'ريال'
    os.environ['WELCOME_MESSAGE'] = 'مرحباً بك في متجرنا! 🛍️'
    os.environ['DEBUG'] = 'False'  # تعطيل وضع التطوير
    
    try:
        # استيراد البوت
        from main import MultiPlatformBot
        
        # إنشاء البوت
        bot = MultiPlatformBot()
        app = bot.flask_app
        
        # إعدادات هادئة
        app.config['DEBUG'] = False
        app.config['ENV'] = 'production'
        
        print("🚀 بدء تشغيل البوت متعدد المنصات...")
        print(f"📱 متجر: {os.getenv('STORE_NAME')}")
        print("🌐 الخادم: http://localhost:5000")
        print("🔧 لوحة الإدارة: http://localhost:5000/admin")
        print("=" * 50)
        print("✅ البوت يعمل بنجاح (بدون تحذيرات)")
        print("🔇 وضع هادئ مُفعل")
        
        # تشغيل الخادم بهدوء
        class QuietHandler(WSGIRequestHandler):
            def log_request(self, code='-', size='-'):
                # إخفاء سجلات الطلبات
                pass
        
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            use_reloader=False,
            request_handler=QuietHandler
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
