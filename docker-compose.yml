version: '3.8'

services:
  multi-platform-bot:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - PORT=5000
      - HOST=0.0.0.0
      - PYTHONUNBUFFERED=1
    env_file:
      - .env
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs:rw
    networks:
      - bot-network
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /app/logs
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE

networks:
  bot-network:
    driver: bridge
