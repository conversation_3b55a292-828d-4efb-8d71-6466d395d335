# استخدام صورة Alpine Linux الأكثر أماناً
FROM python:3.12-alpine

# إعداد متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# تحديث النظام وتثبيت الحزم الأساسية (Alpine)
RUN apk update && apk add --no-cache \
    gcc \
    musl-dev \
    linux-headers

# إنشاء مستخدم غير جذر للأمان
RUN groupadd -r appuser && useradd -r -g appuser appuser

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات المتطلبات أولاً (للاستفادة من Docker cache)
COPY requirements.txt .

# تثبيت المتطلبات
RUN pip install --no-cache-dir -r requirements.txt

# نسخ كود التطبيق
COPY . .

# إنشاء مجلد السجلات وتعيين الصلاحيات
RUN mkdir -p logs && \
    chown -R appuser:appuser /app

# التبديل للمستخدم غير الجذر
USER appuser

# تعيين المنفذ
EXPOSE 5000

# فحص صحة التطبيق
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:5000/health')" || exit 1

# تشغيل التطبيق
CMD ["python", "run_fixed.py"]
