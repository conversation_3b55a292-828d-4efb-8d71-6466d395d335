#!/usr/bin/env python3
"""
اختبار بوت Telegram
"""

import asyncio
import logging
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes

# إعداد السجلات
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# التوكن
TELEGRAM_BOT_TOKEN = "**********************************************"
ADMIN_ID = "5289050159"

async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """أمر البداية"""
    user = update.effective_user
    welcome_msg = f"""
مرحباً {user.first_name}! 👋

مرحباً بك في متجر الأناقة للملابس!

يمكنني مساعدتك في:
🛍️ عرض المنتجات
📏 معرفة المقاسات  
🚚 معلومات التوصيل
💰 الأسعار والعروض

اكتب "منتجات" لعرض أحدث المنتجات
    """
    
    await update.message.reply_text(welcome_msg)
    
    # إشعار المسؤول
    try:
        await context.bot.send_message(
            chat_id=ADMIN_ID,
            text=f"🔔 مستخدم جديد: {user.first_name} (@{user.username})"
        )
    except Exception as e:
        logger.error(f"خطأ في إرسال إشعار للمسؤول: {e}")

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """أمر المساعدة"""
    help_msg = """
📋 الأوامر المتاحة:

🔹 /start - بدء المحادثة
🔹 /help - عرض هذه المساعدة
🔹 /products - عرض المنتجات
🔹 "مرحبا" - رسالة الترحيب
🔹 "منتجات" - عرض المنتجات
🔹 "طلب + رقم" - طلب منتج
🔹 "توصيل" - معلومات التوصيل

📞 للتواصل المباشر: 0501234567
    """
    await update.message.reply_text(help_msg)

async def products_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """أمر عرض المنتجات"""
    products_msg = """
🛍️ منتجات متجر الأناقة:

1️⃣ قمصان رجالية - 120 ريال
2️⃣ فساتين نسائية - 180 ريال  
3️⃣ بناطيل جينز - 150 ريال
4️⃣ أحذية رياضية - 200 ريال
5️⃣ حقائب يد - 90 ريال
6️⃣ ساعات - 250 ريال

💡 للطلب اكتب: طلب + رقم المنتج
مثال: طلب 1
    """
    await update.message.reply_text(products_msg)

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """معالجة الرسائل النصية"""
    user = update.effective_user
    message_text = update.message.text.lower()
    
    logger.info(f"رسالة من {user.first_name}: {message_text}")
    
    # رسائل الترحيب
    if any(word in message_text for word in ['مرحبا', 'السلام', 'هلا', 'أهلا']):
        response = f"""
مرحباً {user.first_name}! 👋

مرحباً بك في متجر الأناقة للملابس!

يمكنني مساعدتك في:
🛍️ عرض المنتجات
📏 معرفة المقاسات
🚚 معلومات التوصيل
💰 الأسعار والعروض

اكتب "منتجات" لعرض أحدث المنتجات
        """
    
    # عرض المنتجات
    elif any(word in message_text for word in ['منتجات', 'كتالوج', 'المنتجات']):
        response = """
🛍️ منتجات متجر الأناقة:

1️⃣ قمصان رجالية - 120 ريال
2️⃣ فساتين نسائية - 180 ريال  
3️⃣ بناطيل جينز - 150 ريال
4️⃣ أحذية رياضية - 200 ريال
5️⃣ حقائب يد - 90 ريال
6️⃣ ساعات - 250 ريال

💡 للطلب اكتب: طلب + رقم المنتج
مثال: طلب 1
        """
    
    # معالجة الطلبات
    elif 'طلب' in message_text:
        import re
        order_match = re.search(r'طلب\s*(\d+)', message_text)
        if order_match:
            product_id = int(order_match.group(1))
            products = {
                1: 'قمصان رجالية - 120 ريال',
                2: 'فساتين نسائية - 180 ريال',
                3: 'بناطيل جينز - 150 ريال',
                4: 'أحذية رياضية - 200 ريال',
                5: 'حقائب يد - 90 ريال',
                6: 'ساعات - 250 ريال'
            }
            
            if product_id in products:
                response = f"""
✅ تم استلام طلبك!

📦 المنتج: {products[product_id]}

سيتواصل معك فريق المبيعات خلال 15 دقيقة لتأكيد:
📱 رقم الهاتف
📍 عنوان التوصيل  
💳 طريقة الدفع

شكراً لثقتك بنا! 🙏
                """
                
                # إشعار المسؤول
                try:
                    await context.bot.send_message(
                        chat_id=ADMIN_ID,
                        text=f"🔔 طلب جديد من {user.first_name} (@{user.username}):\n{products[product_id]}"
                    )
                except Exception as e:
                    logger.error(f"خطأ في إرسال إشعار للمسؤول: {e}")
            else:
                response = f"❌ عذراً، المنتج رقم {product_id} غير موجود.\n\nاكتب 'منتجات' لعرض المنتجات المتاحة."
        else:
            response = "يرجى كتابة رقم المنتج بعد كلمة 'طلب'\nمثال: طلب 1"
    
    # معلومات التوصيل
    elif any(word in message_text for word in ['توصيل', 'شحن', 'التوصيل']):
        response = """
🚚 معلومات التوصيل:

📍 داخل الرياض: 15 ريال (24 ساعة)
📍 خارج الرياض: 25 ريال (2-3 أيام)
📍 طلبات أكثر من 300 ريال: توصيل مجاني!

💳 طرق الدفع:
• الدفع عند الاستلام
• تحويل بنكي
• مدى/فيزا
        """
    
    # المساعدة
    elif any(word in message_text for word in ['مساعدة', 'help', 'أوامر']):
        response = """
📋 الأوامر المتاحة:

🔹 /start - بدء المحادثة
🔹 /help - عرض هذه المساعدة
🔹 /products - عرض المنتجات
🔹 "مرحبا" - رسالة الترحيب
🔹 "منتجات" - عرض المنتجات
🔹 "طلب + رقم" - طلب منتج
🔹 "توصيل" - معلومات التوصيل

📞 للتواصل المباشر: 0501234567
        """
    
    # الرد الافتراضي
    else:
        response = """
عذراً، لم أفهم طلبك 🤔

يمكنك كتابة:
• "منتجات" - لعرض المنتجات
• "توصيل" - لمعلومات التوصيل  
• "طلب + رقم" - لطلب منتج
• "مساعدة" - لعرض جميع الأوامر

أو تواصل مع خدمة العملاء: 📞 0501234567
        """
    
    await update.message.reply_text(response)

async def error_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """معالج الأخطاء"""
    logger.error(f"خطأ في التحديث {update}: {context.error}")

def main():
    """تشغيل البوت"""
    if not TELEGRAM_BOT_TOKEN:
        logger.error("TELEGRAM_BOT_TOKEN غير موجود")
        return
    
    # إنشاء التطبيق
    application = Application.builder().token(TELEGRAM_BOT_TOKEN).build()
    
    # إضافة المعالجات
    application.add_handler(CommandHandler("start", start_command))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("products", products_command))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))
    application.add_error_handler(error_handler)
    
    logger.info("🚀 بدء تشغيل بوت Telegram...")
    logger.info(f"🤖 اسم البوت: متجر الأناقة")
    
    # تشغيل البوت
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == "__main__":
    main()
