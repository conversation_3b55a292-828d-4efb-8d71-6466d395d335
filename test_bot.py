"""
اختبار البوت متعدد المنصات
"""
import unittest
from unittest.mock import patch, MagicMock
from utils.message_handler import MessageHandler
from config import config

class TestMessageHandler(unittest.TestCase):
    """اختبار معالج الرسائل"""
    
    def setUp(self):
        self.handler = MessageHandler()
    
    def test_welcome_message(self):
        """اختبار رسالة الترحيب"""
        response = self.handler.process_message("مرحبا")
        self.assertIn("مرحباً بك", response)
        self.assertIn(config.STORE_NAME, response)
    
    def test_products_message(self):
        """اختبار رسالة المنتجات"""
        response = self.handler.process_message("منتجات")
        self.assertIn("منتجات", response)
        self.assertIn("قمصان رجالية", response)
    
    def test_order_processing(self):
        """اختبار معالجة الطلبات"""
        response = self.handler.process_message("طلب 1")
        self.assertIn("تم استلام طلبك", response)
        self.assertIn("قمصان رجالية", response)
    
    def test_invalid_order(self):
        """اختبار طلب غير صحيح"""
        response = self.handler.process_message("طلب 999")
        self.assertIn("غير موجود", response)
    
    def test_shipping_info(self):
        """اختبار معلومات التوصيل"""
        response = self.handler.process_message("توصيل")
        self.assertIn("معلومات التوصيل", response)
        self.assertIn("الرياض", response)
    
    def test_help_message(self):
        """اختبار رسالة المساعدة"""
        response = self.handler.process_message("مساعدة")
        self.assertIn("الأوامر المتاحة", response)
    
    def test_default_message(self):
        """اختبار الرسالة الافتراضية"""
        response = self.handler.process_message("رسالة غير مفهومة")
        self.assertIn("لم أفهم طلبك", response)

class TestConfig(unittest.TestCase):
    """اختبار الإعدادات"""
    
    def test_products_exist(self):
        """اختبار وجود المنتجات"""
        self.assertGreater(len(config.PRODUCTS), 0)
        self.assertIn('id', config.PRODUCTS[0])
        self.assertIn('name', config.PRODUCTS[0])
        self.assertIn('price', config.PRODUCTS[0])
    
    def test_shipping_info_exists(self):
        """اختبار وجود معلومات التوصيل"""
        self.assertIn('riyadh', config.SHIPPING_INFO)
        self.assertIn('outside_riyadh', config.SHIPPING_INFO)
    
    def test_store_name_exists(self):
        """اختبار وجود اسم المتجر"""
        self.assertIsNotNone(config.STORE_NAME)
        self.assertGreater(len(config.STORE_NAME), 0)

if __name__ == '__main__':
    print("🧪 بدء اختبار البوت...")
    unittest.main(verbosity=2)
