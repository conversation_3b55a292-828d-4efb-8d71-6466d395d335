"""
بوت WhatsApp باستخدام Twilio
"""
import logging
from flask import Flask, request
from twilio.rest import Client
from twilio.twiml.messaging_response import MessagingResponse
from config import config
from utils.message_handler import MessageHandler

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WhatsAppBot:
    """فئة بوت WhatsApp"""
    
    def __init__(self, flask_app=None):
        self.client = None
        self.message_handler = MessageHandler()
        self.flask_app = flask_app or Flask(__name__)
        self.setup_client()
        self.setup_routes()
    
    def setup_client(self):
        """إعداد عميل Twilio"""
        if config.TWILIO_ACCOUNT_SID and config.TWILIO_AUTH_TOKEN:
            self.client = Client(config.TWILIO_ACCOUNT_SID, config.TWILIO_AUTH_TOKEN)
            logger.info("✅ تم إعداد عميل Twilio بنجاح")
        else:
            logger.error("❌ معرفات Twilio غير موجودة في متغيرات البيئة")
    
    def setup_routes(self):
        """إعداد مسارات Flask"""
        
        @self.flask_app.route('/whatsapp', methods=['POST'])
        def whatsapp_webhook():
            """webhook لاستقبال رسائل WhatsApp"""
            return self.handle_whatsapp_message()
        
        @self.flask_app.route('/whatsapp/status', methods=['POST'])
        def whatsapp_status():
            """webhook لحالة الرسائل"""
            return self.handle_message_status()
        
        @self.flask_app.route('/health', methods=['GET'])
        def health_check():
            """فحص صحة الخدمة"""
            return {
                'status': 'healthy',
                'service': 'WhatsApp Bot',
                'store': config.STORE_NAME
            }
    
    def handle_whatsapp_message(self):
        """معالجة رسائل WhatsApp الواردة"""
        try:
            # الحصول على بيانات الرسالة
            incoming_msg = request.values.get('Body', '').strip()
            from_number = request.values.get('From', '')
            
            logger.info(f"رسالة من {from_number}: {incoming_msg}")
            
            # معالجة الرسالة
            response_text = self.message_handler.process_message(incoming_msg)
            
            # إنشاء رد Twilio
            resp = MessagingResponse()
            resp.message(response_text)
            
            # إشعار المسؤول بالطلبات
            if 'طلب' in incoming_msg.lower():
                self.notify_admin(f"طلب جديد من {from_number}:\n{incoming_msg}")
            
            return str(resp)
            
        except Exception as e:
            logger.error(f"خطأ في معالجة رسالة WhatsApp: {e}")
            resp = MessagingResponse()
            resp.message("عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.")
            return str(resp)
    
    def handle_message_status(self):
        """معالجة حالة الرسائل"""
        message_sid = request.values.get('MessageSid')
        message_status = request.values.get('MessageStatus')
        
        logger.info(f"حالة الرسالة {message_sid}: {message_status}")
        
        return "OK"
    
    def send_message(self, to_number, message_text):
        """إرسال رسالة WhatsApp"""
        if not self.client:
            logger.error("عميل Twilio غير متاح")
            return False
        
        try:
            message = self.client.messages.create(
                body=message_text,
                from_=config.TWILIO_WHATSAPP_NUMBER,
                to=to_number
            )
            
            logger.info(f"تم إرسال رسالة إلى {to_number}: {message.sid}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إرسال رسالة WhatsApp: {e}")
            return False
    
    def notify_admin(self, message):
        """إشعار المسؤول"""
        if config.ADMIN_WHATSAPP_NUMBER:
            self.send_message(
                config.ADMIN_WHATSAPP_NUMBER,
                f"🔔 {message}"
            )
    
    def broadcast_message(self, numbers, message):
        """إرسال رسالة جماعية"""
        results = []
        for number in numbers:
            result = self.send_message(number, message)
            results.append({'number': number, 'success': result})
        
        return results
    
    def get_flask_app(self):
        """الحصول على تطبيق Flask"""
        return self.flask_app

# إنشاء مثيل البوت
whatsapp_bot = WhatsAppBot()

def create_whatsapp_app():
    """إنشاء تطبيق WhatsApp"""
    return whatsapp_bot.get_flask_app()

if __name__ == "__main__":
    app = whatsapp_bot.get_flask_app()
    
    print(f"🚀 بدء تشغيل بوت WhatsApp...")
    print(f"📱 متجر: {config.STORE_NAME}")
    print(f"🌐 الخادم: http://{config.HOST}:{config.PORT}")
    
    app.run(
        host=config.HOST,
        port=config.PORT,
        debug=config.DEBUG
    )
