#!/usr/bin/env python3
"""
تشغيل البوت في وضع العرض التوضيحي
يعمل بدون الحاجة لتوكنات حقيقية
"""

import os
import sys
from flask import Flask, render_template, jsonify, request
from utils.message_handler import MessageHandler

# إعداد متغيرات البيئة للعرض التوضيحي
os.environ['STORE_NAME'] = 'متجر الأناقة للملابس'
os.environ['CURRENCY'] = 'ريال'
os.environ['WELCOME_MESSAGE'] = 'مرحباً بك في متجرنا! 🛍️'
os.environ['DEBUG'] = 'True'

app = Flask(__name__)
message_handler = MessageHandler()

@app.route('/')
def home():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/admin')
def admin():
    """صفحة الإدارة"""
    return render_template('admin.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """API للدردشة مع البوت"""
    try:
        data = request.get_json()
        if not data or 'message' not in data:
            return jsonify({'error': 'الرسالة مطلوبة'}), 400
        
        user_message = data['message']
        bot_response = message_handler.process_message(user_message)
        
        return jsonify({
            'response': bot_response,
            'status': 'success'
        })
    except Exception as e:
        print(f"خطأ في API الدردشة: {e}")
        return jsonify({
            'error': 'حدث خطأ في معالجة الرسالة',
            'status': 'error'
        }), 500

@app.route('/api/products')
def get_products():
    """API للمنتجات"""
    from config import config
    return jsonify({
        'store': config.STORE_NAME,
        'products': config.PRODUCTS,
        'currency': config.CURRENCY
    })

@app.route('/api/shipping')
def get_shipping():
    """API لمعلومات التوصيل"""
    from config import config
    return jsonify({
        'store': config.STORE_NAME,
        'shipping': config.SHIPPING_INFO,
        'payment_methods': config.PAYMENT_METHODS,
        'currency': config.CURRENCY
    })

@app.route('/health')
def health():
    """فحص صحة الخدمة"""
    return jsonify({
        'status': 'healthy',
        'service': 'Multi-Platform Bot Demo',
        'mode': 'demonstration'
    })

@app.route('/api/admin/stats')
def admin_stats():
    """إحصائيات الإدارة"""
    return jsonify({
        'total_messages': 150,
        'active_users': 25,
        'total_orders': 12,
        'uptime': '2h 30m'
    })

@app.route('/api/admin/status')
def admin_status():
    """حالة الخدمات"""
    return jsonify({
        'telegram': True,  # وضع العرض التوضيحي
        'whatsapp': True,
        'database': True,
        'api': True
    })

@app.route('/api/admin/logs')
def admin_logs():
    """سجل النشاط"""
    import datetime
    logs = [
        {
            'timestamp': datetime.datetime.now().isoformat(),
            'message': 'تم بدء تشغيل البوت في وضع العرض التوضيحي'
        },
        {
            'timestamp': (datetime.datetime.now() - datetime.timedelta(minutes=2)).isoformat(),
            'message': 'تم استلام رسالة تجريبية من المستخدم'
        },
        {
            'timestamp': (datetime.datetime.now() - datetime.timedelta(minutes=5)).isoformat(),
            'message': 'تم معالجة طلب تجريبي بنجاح'
        },
        {
            'timestamp': (datetime.datetime.now() - datetime.timedelta(minutes=8)).isoformat(),
            'message': 'تم تحديث إعدادات البوت'
        }
    ]
    return jsonify({'logs': logs})

@app.route('/api/admin/settings', methods=['POST'])
def admin_settings():
    """حفظ الإعدادات"""
    try:
        data = request.get_json()
        print(f"تم تحديث الإعدادات: {data}")
        return jsonify({'status': 'success'})
    except Exception as e:
        print(f"خطأ في حفظ الإعدادات: {e}")
        return jsonify({'error': 'فشل في حفظ الإعدادات'}), 500

@app.route('/api/admin/broadcast', methods=['POST'])
def admin_broadcast():
    """إرسال رسالة جماعية"""
    try:
        data = request.get_json()
        platform = data.get('platform', 'all')
        message = data.get('message', '')
        
        print(f"إرسال رسالة جماعية على {platform}: {message}")
        
        return jsonify({
            'status': 'success',
            'message': 'تم إرسال الرسالة بنجاح (وضع العرض التوضيحي)'
        })
    except Exception as e:
        print(f"خطأ في الإرسال الجماعي: {e}")
        return jsonify({'error': 'فشل في إرسال الرسالة'}), 500

if __name__ == '__main__':
    print("🚀 بدء تشغيل البوت في وضع العرض التوضيحي...")
    print("📱 متجر: متجر الأناقة للملابس")
    print("🌐 الخادم: http://localhost:5000")
    print("🔧 لوحة الإدارة: http://localhost:5000/admin")
    print("=" * 50)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )
