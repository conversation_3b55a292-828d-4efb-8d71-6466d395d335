<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة إدارة البوت - متجر الأناقة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
        }
        .activity-log {
            max-height: 400px;
            overflow-y: auto;
        }
        .log-entry {
            border-left: 4px solid #007bff;
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .log-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-robot"></i> لوحة إدارة البوت
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home"></i> الصفحة الرئيسية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- إحصائيات سريعة -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-comments fa-2x mb-2"></i>
                    <div class="stats-number" id="totalMessages">0</div>
                    <div>إجمالي الرسائل</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-users fa-2x mb-2"></i>
                    <div class="stats-number" id="activeUsers">0</div>
                    <div>المستخدمين النشطين</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                    <div class="stats-number" id="totalOrders">0</div>
                    <div>إجمالي الطلبات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <i class="fas fa-clock fa-2x mb-2"></i>
                    <div class="stats-number" id="uptime">0h</div>
                    <div>وقت التشغيل</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- حالة الخدمات -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-server"></i> حالة الخدمات</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span><i class="fab fa-telegram"></i> Telegram Bot</span>
                            <span class="badge bg-success" id="telegramStatus">متصل</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span><i class="fab fa-whatsapp"></i> WhatsApp Bot</span>
                            <span class="badge bg-success" id="whatsappStatus">متصل</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span><i class="fas fa-database"></i> قاعدة البيانات</span>
                            <span class="badge bg-success" id="databaseStatus">متصل</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-globe"></i> API Server</span>
                            <span class="badge bg-success" id="apiStatus">يعمل</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات سريعة -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> إعدادات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">رسالة الترحيب</label>
                            <textarea class="form-control" id="welcomeMessage" rows="3">مرحباً بك في متجر الأناقة للملابس! 👋</textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">وضع الصيانة</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="maintenanceMode">
                                <label class="form-check-label" for="maintenanceMode">
                                    تفعيل وضع الصيانة
                                </label>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="saveSettings()">
                            <i class="fas fa-save"></i> حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- سجل النشاط -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list"></i> سجل النشاط</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshLogs()">
                            <i class="fas fa-refresh"></i> تحديث
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="activity-log" id="activityLog">
                            <!-- سيتم تحميل السجلات هنا -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إرسال رسالة جماعية -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bullhorn"></i> إرسال رسالة جماعية</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">المنصة</label>
                            <select class="form-select" id="broadcastPlatform">
                                <option value="all">جميع المنصات</option>
                                <option value="telegram">Telegram فقط</option>
                                <option value="whatsapp">WhatsApp فقط</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الرسالة</label>
                            <textarea class="form-control" id="broadcastMessage" rows="4" 
                                      placeholder="اكتب رسالتك هنا..."></textarea>
                        </div>
                        <button class="btn btn-warning" onclick="sendBroadcast()">
                            <i class="fas fa-paper-plane"></i> إرسال للجميع
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الإحصائيات
        async function updateStats() {
            try {
                const response = await fetch('/api/admin/stats');
                const data = await response.json();
                
                document.getElementById('totalMessages').textContent = data.total_messages || 0;
                document.getElementById('activeUsers').textContent = data.active_users || 0;
                document.getElementById('totalOrders').textContent = data.total_orders || 0;
                document.getElementById('uptime').textContent = data.uptime || '0h';
            } catch (error) {
                console.error('خطأ في تحديث الإحصائيات:', error);
            }
        }

        // تحديث حالة الخدمات
        async function updateServiceStatus() {
            try {
                const response = await fetch('/api/admin/status');
                const data = await response.json();
                
                updateStatusBadge('telegramStatus', data.telegram);
                updateStatusBadge('whatsappStatus', data.whatsapp);
                updateStatusBadge('databaseStatus', data.database);
                updateStatusBadge('apiStatus', data.api);
            } catch (error) {
                console.error('خطأ في تحديث حالة الخدمات:', error);
            }
        }

        function updateStatusBadge(elementId, status) {
            const element = document.getElementById(elementId);
            if (status) {
                element.className = 'badge bg-success';
                element.textContent = 'متصل';
            } else {
                element.className = 'badge bg-danger';
                element.textContent = 'غير متصل';
            }
        }

        // تحديث سجل النشاط
        async function refreshLogs() {
            try {
                const response = await fetch('/api/admin/logs');
                const data = await response.json();
                
                const logContainer = document.getElementById('activityLog');
                logContainer.innerHTML = '';
                
                data.logs.forEach(log => {
                    const logEntry = document.createElement('div');
                    logEntry.className = 'log-entry';
                    logEntry.innerHTML = `
                        <div class="log-time">${new Date(log.timestamp).toLocaleString('ar-SA')}</div>
                        <div>${log.message}</div>
                    `;
                    logContainer.appendChild(logEntry);
                });
            } catch (error) {
                console.error('خطأ في تحديث السجلات:', error);
            }
        }

        // حفظ الإعدادات
        async function saveSettings() {
            const welcomeMessage = document.getElementById('welcomeMessage').value;
            const maintenanceMode = document.getElementById('maintenanceMode').checked;
            
            try {
                const response = await fetch('/api/admin/settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        welcome_message: welcomeMessage,
                        maintenance_mode: maintenanceMode
                    })
                });
                
                if (response.ok) {
                    alert('تم حفظ الإعدادات بنجاح!');
                } else {
                    alert('حدث خطأ في حفظ الإعدادات');
                }
            } catch (error) {
                console.error('خطأ في حفظ الإعدادات:', error);
                alert('حدث خطأ في الاتصال');
            }
        }

        // إرسال رسالة جماعية
        async function sendBroadcast() {
            const platform = document.getElementById('broadcastPlatform').value;
            const message = document.getElementById('broadcastMessage').value;
            
            if (!message.trim()) {
                alert('يرجى كتابة الرسالة');
                return;
            }
            
            if (!confirm('هل أنت متأكد من إرسال هذه الرسالة لجميع المستخدمين؟')) {
                return;
            }
            
            try {
                const response = await fetch('/api/admin/broadcast', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        platform: platform,
                        message: message
                    })
                });
                
                if (response.ok) {
                    alert('تم إرسال الرسالة بنجاح!');
                    document.getElementById('broadcastMessage').value = '';
                } else {
                    alert('حدث خطأ في إرسال الرسالة');
                }
            } catch (error) {
                console.error('خطأ في إرسال الرسالة:', error);
                alert('حدث خطأ في الاتصال');
            }
        }

        // تحديث البيانات كل 30 ثانية
        setInterval(() => {
            updateStats();
            updateServiceStatus();
        }, 30000);

        // تحميل البيانات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateStats();
            updateServiceStatus();
            refreshLogs();
        });
    </script>
</body>
</html>
