"""
معالج الرسائل المشترك بين المنصات
"""
import re
from config import config

class MessageHandler:
    """معالج الرسائل الموحد"""
    
    @staticmethod
    def get_welcome_message():
        """رسالة الترحيب"""
        return f"""
{config.WELCOME_MESSAGE}

مرحباً بك في {config.STORE_NAME}! 👋

يمكنني مساعدتك في:
🛍️ عرض المنتجات
📏 معرفة المقاسات
🚚 معلومات التوصيل
💰 الأسعار والعروض

اكتب "منتجات" لعرض أحدث المنتجات
اكتب "مساعدة" لعرض جميع الأوامر
        """
    
    @staticmethod
    def get_products_message():
        """رسالة عرض المنتجات"""
        message = f"🛍️ منتجات {config.STORE_NAME}:\n\n"
        
        for product in config.PRODUCTS:
            message += f"{product['id']}️⃣ {product['name']} - {product['price']} {config.CURRENCY}\n"
        
        message += f"\n💡 للطلب اكتب: طلب + رقم المنتج\n"
        message += f"مثال: طلب 1"
        
        return message
    
    @staticmethod
    def get_shipping_message():
        """رسالة معلومات التوصيل"""
        shipping = config.SHIPPING_INFO
        return f"""
🚚 معلومات التوصيل:

📍 داخل الرياض: {shipping['riyadh']['price']} {config.CURRENCY} ({shipping['riyadh']['duration']})
📍 خارج الرياض: {shipping['outside_riyadh']['price']} {config.CURRENCY} ({shipping['outside_riyadh']['duration']})
📍 طلبات أكثر من {shipping['free_shipping_threshold']} {config.CURRENCY}: توصيل مجاني!

💳 طرق الدفع:
{chr(10).join(f'• {method}' for method in config.PAYMENT_METHODS)}
        """
    
    @staticmethod
    def get_help_message():
        """رسالة المساعدة"""
        return """
📋 الأوامر المتاحة:

🔹 "مرحبا" أو "السلام" - رسالة الترحيب
🔹 "منتجات" أو "كتالوج" - عرض المنتجات
🔹 "طلب + رقم" - طلب منتج (مثال: طلب 1)
🔹 "توصيل" أو "شحن" - معلومات التوصيل
🔹 "مساعدة" - عرض هذه القائمة

📞 للتواصل المباشر: 0501234567
        """
    
    @staticmethod
    def process_order(message_text):
        """معالجة الطلبات"""
        # البحث عن رقم المنتج في الرسالة
        order_match = re.search(r'طلب\s*(\d+)', message_text)
        if order_match:
            product_id = int(order_match.group(1))
            
            # البحث عن المنتج
            product = next((p for p in config.PRODUCTS if p['id'] == product_id), None)
            
            if product:
                return f"""
✅ تم استلام طلبك!

📦 المنتج: {product['name']}
💰 السعر: {product['price']} {config.CURRENCY}

سيتواصل معك فريق المبيعات خلال 15 دقيقة لتأكيد:
📱 رقم الهاتف
📍 عنوان التوصيل  
💳 طريقة الدفع

شكراً لثقتك بنا! 🙏
                """
            else:
                return f"""
❌ عذراً، المنتج رقم {product_id} غير موجود.

اكتب "منتجات" لعرض المنتجات المتاحة.
                """
        
        return None
    
    @staticmethod
    def get_default_message():
        """الرسالة الافتراضية"""
        return """
عذراً، لم أفهم طلبك 🤔

يمكنك كتابة:
• "منتجات" - لعرض المنتجات
• "توصيل" - لمعلومات التوصيل  
• "طلب + رقم" - لطلب منتج
• "مساعدة" - لعرض جميع الأوامر

أو تواصل مع خدمة العملاء: 📞 0501234567
        """
    
    @staticmethod
    def process_message(message_text):
        """معالجة الرسالة وإرجاع الرد المناسب"""
        message_text = message_text.lower().strip()
        
        # رسائل الترحيب
        if any(word in message_text for word in ['مرحبا', 'السلام', 'هلا', 'أهلا']):
            return MessageHandler.get_welcome_message()
        
        # عرض المنتجات
        elif any(word in message_text for word in ['منتجات', 'كتالوج', 'المنتجات']):
            return MessageHandler.get_products_message()
        
        # معالجة الطلبات
        elif 'طلب' in message_text:
            order_response = MessageHandler.process_order(message_text)
            return order_response if order_response else MessageHandler.get_default_message()
        
        # معلومات التوصيل
        elif any(word in message_text for word in ['توصيل', 'شحن', 'التوصيل']):
            return MessageHandler.get_shipping_message()
        
        # المساعدة
        elif any(word in message_text for word in ['مساعدة', 'help', 'أوامر']):
            return MessageHandler.get_help_message()
        
        # الرد الافتراضي
        else:
            return MessageHandler.get_default_message()
