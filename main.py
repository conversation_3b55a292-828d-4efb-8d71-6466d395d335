"""
البوت الرئيسي متعدد المنصات
يدعم WhatsApp و Telegram
"""
import asyncio
import threading
import logging
from flask import Flask, jsonify, render_template, request
from config import config
from platforms.telegram_bot import run_telegram_bot
from platforms.whatsapp_bot import create_whatsapp_app
from utils.message_handler import MessageHandler

# إعداد السجلات
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)


class MultiPlatformBot:
    """البوت متعدد المنصات"""

    def __init__(self):
        self.telegram_app = None
        self.whatsapp_app = None
        self.flask_app = Flask(__name__)
        self.message_handler = MessageHandler()
        self.setup_main_routes()

    def setup_main_routes(self):
        """إعداد المسارات الرئيسية"""

        @self.flask_app.route('/')
        def home():
            """الصفحة الرئيسية"""
            return render_template('index.html')

        @self.flask_app.route('/api/info')
        def api_info():
            """معلومات API"""
            return jsonify({
                'message': f'مرحباً بك في {config.STORE_NAME}',
                'status': 'البوت يعمل بنجاح',
                'platforms': {
                    'telegram': bool(config.TELEGRAM_BOT_TOKEN),
                    'whatsapp': bool(config.TWILIO_ACCOUNT_SID)
                },
                'endpoints': {
                    'whatsapp_webhook': '/whatsapp',
                    'health_check': '/health',
                    'products': '/api/products',
                    'shipping': '/api/shipping',
                    'chat': '/api/chat'
                }
            })

        @self.flask_app.route('/api/chat', methods=['POST'])
        def chat():
            """API للدردشة مع البوت"""
            try:
                data = request.get_json()
                if not data or 'message' not in data:
                    return jsonify({'error': 'الرسالة مطلوبة'}), 400

                user_message = data['message']
                bot_response = self.message_handler.process_message(
                    user_message)

                return jsonify({
                    'response': bot_response,
                    'status': 'success'
                })
            except Exception as e:
                logger.error(f"خطأ في API الدردشة: {e}")
                return jsonify({
                    'error': 'حدث خطأ في معالجة الرسالة',
                    'status': 'error'
                }), 500

        @self.flask_app.route('/admin')
        def admin():
            """صفحة الإدارة"""
            return render_template('admin.html')

        @self.flask_app.route('/api/admin/stats')
        def admin_stats():
            """إحصائيات الإدارة"""
            return jsonify({
                'total_messages': 150,  # يمكن ربطها بقاعدة بيانات
                'active_users': 25,
                'total_orders': 12,
                'uptime': '2h 30m'
            })

        @self.flask_app.route('/api/admin/status')
        def admin_status():
            """حالة الخدمات"""
            return jsonify({
                'telegram': bool(config.TELEGRAM_BOT_TOKEN),
                'whatsapp': bool(config.TWILIO_ACCOUNT_SID),
                'database': True,
                'api': True
            })

        @self.flask_app.route('/api/admin/logs')
        def admin_logs():
            """سجل النشاط"""
            import datetime
            logs = [
                {
                    'timestamp': datetime.datetime.now().isoformat(),
                    'message': 'تم بدء تشغيل البوت بنجاح'
                },
                {
                    'timestamp': (datetime.datetime.now() - datetime.timedelta(minutes=5)).isoformat(),
                    'message': 'تم استلام رسالة جديدة من مستخدم'
                },
                {
                    'timestamp': (datetime.datetime.now() - datetime.timedelta(minutes=10)).isoformat(),
                    'message': 'تم معالجة طلب جديد'
                }
            ]
            return jsonify({'logs': logs})

        @self.flask_app.route('/api/admin/settings', methods=['POST'])
        def admin_settings():
            """حفظ الإعدادات"""
            try:
                data = request.get_json()
                # هنا يمكن حفظ الإعدادات في قاعدة البيانات
                logger.info(f"تم تحديث الإعدادات: {data}")
                return jsonify({'status': 'success'})
            except Exception as e:
                logger.error(f"خطأ في حفظ الإعدادات: {e}")
                return jsonify({'error': 'فشل في حفظ الإعدادات'}), 500

        @self.flask_app.route('/api/admin/broadcast', methods=['POST'])
        def admin_broadcast():
            """إرسال رسالة جماعية"""
            try:
                data = request.get_json()
                platform = data.get('platform', 'all')
                message = data.get('message', '')

                # هنا يمكن إضافة منطق الإرسال الجماعي
                logger.info(f"إرسال رسالة جماعية على {platform}: {message}")

                return jsonify({
                    'status': 'success',
                    'message': 'تم إرسال الرسالة بنجاح'
                })
            except Exception as e:
                logger.error(f"خطأ في الإرسال الجماعي: {e}")
                return jsonify({'error': 'فشل في إرسال الرسالة'}), 500

        @self.flask_app.route('/api/products')
        def get_products():
            """API للمنتجات"""
            return jsonify({
                'store': config.STORE_NAME,
                'products': config.PRODUCTS,
                'currency': config.CURRENCY
            })

        @self.flask_app.route('/api/shipping')
        def get_shipping():
            """API لمعلومات التوصيل"""
            return jsonify({
                'store': config.STORE_NAME,
                'shipping': config.SHIPPING_INFO,
                'payment_methods': config.PAYMENT_METHODS,
                'currency': config.CURRENCY
            })

        @self.flask_app.route('/health')
        def health():
            """فحص صحة الخدمة"""
            return jsonify({
                'status': 'healthy',
                'service': 'Multi-Platform Bot',
                'store': config.STORE_NAME,
                'platforms': {
                    'telegram': 'active' if self.telegram_app else 'inactive',
                    'whatsapp': 'active' if self.whatsapp_app else 'inactive'
                }
            })

    async def start_telegram(self):
        """بدء بوت Telegram"""
        if config.TELEGRAM_BOT_TOKEN:
            logger.info("🚀 بدء تشغيل بوت Telegram...")
            try:
                self.telegram_app = await run_telegram_bot()
                logger.info("✅ بوت Telegram يعمل بنجاح")
            except Exception as e:
                logger.error(f"❌ خطأ في تشغيل بوت Telegram: {e}")
        else:
            logger.warning(
                "⚠️ TELEGRAM_BOT_TOKEN غير موجود - تم تخطي Telegram")

    def start_whatsapp(self):
        """بدء بوت WhatsApp"""
        if config.TWILIO_ACCOUNT_SID and config.TWILIO_AUTH_TOKEN:
            logger.info("🚀 بدء تشغيل بوت WhatsApp...")
            try:
                # دمج مسارات WhatsApp مع التطبيق الرئيسي
                whatsapp_app = create_whatsapp_app()

                # نسخ مسارات WhatsApp إلى التطبيق الرئيسي
                for rule in whatsapp_app.url_map.iter_rules():
                    if rule.endpoint not in ['static', 'home', 'get_products', 'get_shipping', 'health']:
                        self.flask_app.add_url_rule(
                            rule.rule,
                            rule.endpoint + '_whatsapp',
                            whatsapp_app.view_functions[rule.endpoint],
                            methods=rule.methods
                        )

                self.whatsapp_app = whatsapp_app
                logger.info("✅ بوت WhatsApp يعمل بنجاح")
            except Exception as e:
                logger.error(f"❌ خطأ في تشغيل بوت WhatsApp: {e}")
        else:
            logger.warning("⚠️ معرفات Twilio غير موجودة - تم تخطي WhatsApp")

    def run_flask_server(self):
        """تشغيل خادم Flask"""
        logger.info(f"🌐 بدء تشغيل الخادم على {config.HOST}:{config.PORT}")
        self.flask_app.run(
            host=config.HOST,
            port=config.PORT,
            debug=config.DEBUG,
            use_reloader=False  # تجنب إعادة التحميل التلقائي
        )

    async def run_async_components(self):
        """تشغيل المكونات غير المتزامنة"""
        # بدء Telegram
        await self.start_telegram()

        # الحفاظ على التطبيق يعمل
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 إيقاف البوت...")
            if self.telegram_app:
                await self.telegram_app.stop()
                await self.telegram_app.shutdown()

    def run(self):
        """تشغيل البوت متعدد المنصات"""
        logger.info("🚀 بدء تشغيل البوت متعدد المنصات...")
        logger.info(f"📱 متجر: {config.STORE_NAME}")

        # بدء WhatsApp (متزامن)
        self.start_whatsapp()

        # تشغيل Telegram في thread منفصل
        def run_telegram_thread():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.start_telegram())
            loop.run_forever()

        if config.TELEGRAM_BOT_TOKEN:
            telegram_thread = threading.Thread(
                target=run_telegram_thread, daemon=True)
            telegram_thread.start()

        # تشغيل خادم Flask (الخيط الرئيسي)
        try:
            self.run_flask_server()
        except KeyboardInterrupt:
            logger.info("🛑 إيقاف البوت...")


if __name__ == "__main__":
    bot = MultiPlatformBot()
    bot.run()
