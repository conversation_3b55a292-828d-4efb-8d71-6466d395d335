#!/usr/bin/env python3
"""
تشغيل البوت في بيئة الإنتاج
باستخدام Gunicorn WSGI Server
"""

import os
import sys
import logging
from werkzeug.middleware.proxy_fix import ProxyFix

# إعداد السجلات للإنتاج
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/production.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

def create_app():
    """إنشاء تطبيق Flask للإنتاج"""
    
    # استيراد التطبيق الرئيسي
    from main import MultiPlatformBot
    
    # إنشاء البوت
    bot = MultiPlatformBot()
    app = bot.flask_app
    
    # إعدادات الإنتاج
    app.config.update(
        SECRET_KEY=os.getenv('SECRET_KEY', 'your-secret-key-here'),
        DEBUG=False,
        TESTING=False,
        ENV='production'
    )
    
    # إضافة ProxyFix للعمل خلف reverse proxy
    app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1)
    
    return app

# إنشاء التطبيق للاستخدام مع Gunicorn
application = create_app()

if __name__ == "__main__":
    # للتشغيل المباشر (للاختبار فقط)
    print("⚠️  هذا للاختبار فقط!")
    print("🚀 للإنتاج استخدم: gunicorn run_production:application")
    application.run(host='0.0.0.0', port=5000, debug=False)
