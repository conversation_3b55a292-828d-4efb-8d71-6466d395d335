"""
إعداد البوت متعدد المنصات
"""
from setuptools import setup, find_packages

try:
    with open("README.md", "r", encoding="utf-8") as fh:
        long_description = fh.read()
except FileNotFoundError:
    long_description = "بوت ذكي متعدد المنصات للواتساب والتيليجرام"

try:
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        requirements = [line.strip() for line in fh if line.strip()
                        and not line.startswith("#")]
except FileNotFoundError:
    requirements = [
        "flask>=2.3.3",
        "python-telegram-bot>=20.7",
        "twilio>=8.10.0",
        "requests>=2.31.0",
        "python-dotenv>=1.0.0",
        "setuptools>=65.0.0"
    ]

setup(
    name="multi-platform-bot",
    version="1.0.0",
    author="Multi-Platform Bot Developer",
    description="بوت ذكي متعدد المنصات للواتساب والتيليجرام",
    long_description=long_description,
    long_description_content_type="text/markdown",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "multi-platform-bot=main:main",
        ],
    },
)
