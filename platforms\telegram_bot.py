"""
بوت Telegram
"""
from utils.message_handler import <PERSON><PERSON><PERSON><PERSON>
from config import config
from telegram.ext import Application, CommandHandler, MessageHandler as TelegramMessageHandler, filters, ContextTypes
from telegram import Update
import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# إعداد السجلات
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)


class TelegramBot:
    """فئة بوت Telegram"""

    def __init__(self):
        self.application = None
        self.message_handler = MessageHandler()

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر البداية"""
        user = update.effective_user
        welcome_msg = self.message_handler.get_welcome_message()

        await update.message.reply_text(
            f"مرحباً {user.first_name}!\n{welcome_msg}"
        )

        # إشعار المسؤول
        await self.notify_admin(f"مستخدم جديد: {user.first_name} (@{user.username})")

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر المساعدة"""
        help_msg = self.message_handler.get_help_message()
        await update.message.reply_text(help_msg)

    async def products_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر عرض المنتجات"""
        products_msg = self.message_handler.get_products_message()
        await update.message.reply_text(products_msg)

    async def shipping_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر معلومات التوصيل"""
        shipping_msg = self.message_handler.get_shipping_message()
        await update.message.reply_text(shipping_msg)

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة الرسائل النصية"""
        user = update.effective_user
        message_text = update.message.text

        logger.info(f"رسالة من {user.first_name}: {message_text}")

        # معالجة الرسالة
        response = self.message_handler.process_message(message_text)

        # إرسال الرد
        await update.message.reply_text(response)

        # إشعار المسؤول بالطلبات
        if 'طلب' in message_text.lower():
            await self.notify_admin(
                f"طلب جديد من {user.first_name} (@{user.username}):\n{message_text}"
            )

    async def notify_admin(self, message):
        """إشعار المسؤول"""
        if config.TELEGRAM_ADMIN_ID:
            try:
                await self.application.bot.send_message(
                    chat_id=config.TELEGRAM_ADMIN_ID,
                    text=f"🔔 {message}"
                )
            except Exception as e:
                logger.error(f"خطأ في إرسال إشعار للمسؤول: {e}")

    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الأخطاء"""
        logger.error(f"خطأ في التحديث {update}: {context.error}")

    def setup_handlers(self):
        """إعداد معالجات الأوامر والرسائل"""
        # أوامر البوت
        self.application.add_handler(
            CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(
            CommandHandler("products", self.products_command))
        self.application.add_handler(
            CommandHandler("shipping", self.shipping_command))

        # معالج الرسائل النصية
        self.application.add_handler(
            TelegramMessageHandler(filters.TEXT & ~filters.COMMAND,
                                   self.handle_message)
        )

        # معالج الأخطاء
        self.application.add_error_handler(self.error_handler)

    def run(self):
        """تشغيل البوت"""
        if not config.TELEGRAM_BOT_TOKEN:
            logger.error("TELEGRAM_BOT_TOKEN غير موجود في متغيرات البيئة")
            return

        # إنشاء التطبيق
        self.application = Application.builder().token(config.TELEGRAM_BOT_TOKEN).build()

        # إعداد المعالجات
        self.setup_handlers()

        logger.info("🚀 بدء تشغيل بوت Telegram...")

        # تشغيل البوت
        self.application.run_polling(allowed_updates=Update.ALL_TYPES)


async def run_telegram_bot():
    """تشغيل بوت Telegram بشكل غير متزامن"""
    bot = TelegramBot()

    if not config.TELEGRAM_BOT_TOKEN:
        logger.error("TELEGRAM_BOT_TOKEN غير موجود في متغيرات البيئة")
        return None

    # إنشاء التطبيق
    bot.application = Application.builder().token(config.TELEGRAM_BOT_TOKEN).build()

    # إعداد المعالجات
    bot.setup_handlers()

    logger.info("🚀 بدء تشغيل بوت Telegram...")

    # بدء البوت
    await bot.application.initialize()
    await bot.application.start()
    await bot.application.updater.start_polling()

    return bot.application

if __name__ == "__main__":
    bot = TelegramBot()
    bot.run()
