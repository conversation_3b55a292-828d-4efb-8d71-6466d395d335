"""
إعدادات البوت متعدد المنصات
"""
import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

class Config:
    """إعدادات التطبيق"""
    
    # إعدادات Telegram
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
    TELEGRAM_ADMIN_ID = os.getenv('TELEGRAM_ADMIN_ID')
    
    # إعدادات WhatsApp (Twilio)
    TWILIO_ACCOUNT_SID = os.getenv('TWILIO_ACCOUNT_SID')
    TWILIO_AUTH_TOKEN = os.getenv('TWILIO_AUTH_TOKEN')
    TWILIO_WHATSAPP_NUMBER = os.getenv('TWILIO_WHATSAPP_NUMBER', 'whatsapp:+***********')
    ADMIN_WHATSAPP_NUMBER = os.getenv('ADMIN_WHATSAPP_NUMBER')
    
    # إعدادات الخادم
    PORT = int(os.getenv('PORT', 5000))
    HOST = os.getenv('HOST', '0.0.0.0')
    DEBUG = os.getenv('DEBUG', 'True').lower() == 'true'
    
    # إعدادات المتجر
    STORE_NAME = os.getenv('STORE_NAME', 'متجر الأناقة للملابس')
    CURRENCY = os.getenv('CURRENCY', 'ريال')
    WELCOME_MESSAGE = os.getenv('WELCOME_MESSAGE', 'مرحباً بك في متجرنا! 🛍️')
    
    # قائمة المنتجات
    PRODUCTS = [
        {'id': 1, 'name': 'قمصان رجالية', 'price': 120},
        {'id': 2, 'name': 'فساتين نسائية', 'price': 180},
        {'id': 3, 'name': 'بناطيل جينز', 'price': 150},
        {'id': 4, 'name': 'أحذية رياضية', 'price': 200},
        {'id': 5, 'name': 'حقائب يد', 'price': 90},
        {'id': 6, 'name': 'ساعات', 'price': 250}
    ]
    
    # معلومات التوصيل
    SHIPPING_INFO = {
        'riyadh': {'price': 15, 'duration': '24 ساعة'},
        'outside_riyadh': {'price': 25, 'duration': '2-3 أيام'},
        'free_shipping_threshold': 300
    }
    
    # طرق الدفع
    PAYMENT_METHODS = [
        'الدفع عند الاستلام',
        'تحويل بنكي',
        'مدى/فيزا'
    ]

# إنشاء مثيل من الإعدادات
config = Config()
