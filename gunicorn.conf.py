# إعدادات Gunicorn للإنتاج

import multiprocessing
import os

# معلومات الخادم
bind = "0.0.0.0:5000"
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 50
preload_app = True
timeout = 30
keepalive = 2

# السجلات
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'

# الأمان
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# الأداء
worker_tmp_dir = "/dev/shm"  # استخدام RAM للملفات المؤقتة (Linux)

# إعادة التشغيل التلقائي
reload = False  # False للإنتاج
reload_engine = "auto"

# العمليات
daemon = False  # False للاستخدام مع Docker
pidfile = "logs/gunicorn.pid"
user = None  # تعيين المستخدم إذا لزم الأمر
group = None

# إعدادات SSL (إذا لزم الأمر)
# keyfile = "path/to/keyfile"
# certfile = "path/to/certfile"

def when_ready(server):
    """عند جاهزية الخادم"""
    server.log.info("🚀 Gunicorn server is ready!")

def worker_int(worker):
    """عند مقاطعة العامل"""
    worker.log.info("🔄 Worker received INT or QUIT signal")

def pre_fork(server, worker):
    """قبل إنشاء عامل جديد"""
    server.log.info(f"👷 Worker spawned (pid: {worker.pid})")

def post_fork(server, worker):
    """بعد إنشاء عامل جديد"""
    server.log.info(f"✅ Worker spawned (pid: {worker.pid})")

def worker_abort(worker):
    """عند إنهاء العامل بشكل مفاجئ"""
    worker.log.info(f"❌ Worker received SIGABRT signal")
