# البوت متعدد المنصات - WhatsApp & Telegram

بوت ذكي يعمل على منصتي WhatsApp و Telegram باستخدام Python، مصمم للمتاجر الإلكترونية وخدمة العملاء.

## المميزات

### 🚀 متعدد المنصات
- **WhatsApp**: باستخدام Twilio API
- **Telegram**: باستخدام python-telegram-bot
- **API REST**: للتكامل مع التطبيقات الأخرى

### 🛍️ مميزات المتجر
- عرض المنتجات والأسعار
- معالجة الطلبات تلقائياً
- معلومات التوصيل والشحن
- إشعارات للمسؤولين
- رسائل ترحيب ومساعدة

### 🔧 سهولة الاستضافة
- يمكن استضافته على أي خادم يدعم Python
- متوافق مع Heroku, Railway, DigitalOcean
- إعدادات مرنة عبر متغيرات البيئة

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd multi-platform-bot
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إعداد متغيرات البيئة
انسخ ملف `.env.example` إلى `.env` وأضف القيم الحقيقية:

```bash
cp .env.example .env
```

### 4. إعداد Telegram Bot
1. تحدث مع [@BotFather](https://t.me/botfather) على Telegram
2. أنشئ بوت جديد واحصل على التوكن
3. أضف التوكن إلى `TELEGRAM_BOT_TOKEN` في ملف `.env`

### 5. إعداد WhatsApp (Twilio)
1. أنشئ حساب على [Twilio](https://www.twilio.com/)
2. احصل على Account SID و Auth Token
3. فعّل WhatsApp Sandbox
4. أضف المعرفات إلى ملف `.env`

## 🚀 تشغيل البوت

### 1. التشغيل العادي (مع تحذيرات التطوير)
```bash
python main.py
```

### 2. التشغيل الهادئ (بدون تحذيرات) ⭐
```bash
python run_quiet.py
```

### 3. النسخة المُصححة
```bash
python run_fixed.py
```

### 4. للإنتاج (مع Gunicorn)
```bash
# تثبيت gunicorn أولاً
pip install gunicorn

# تشغيل الخادم
gunicorn -c gunicorn.conf.py run_production:application
```

### 5. التشخيص
```bash
python diagnose.py
```

## ⚠️ **حول تحذير Flask:**

التحذير التالي **عادي** في التطوير:
```
WARNING: This is a development server. Do not use it in a production deployment.
```

**الحلول:**
- **للتطوير**: استخدم `python run_quiet.py` (بدون تحذيرات) ⭐
- **للإنتاج**: استخدم `gunicorn` كما هو موضح أعلاه

### تشغيل منصة واحدة فقط
```bash
# Telegram فقط
python platforms/telegram_bot.py

# WhatsApp فقط  
python platforms/whatsapp_bot.py
```

## الاستضافة

### Heroku
1. أنشئ تطبيق جديد على Heroku
2. أضف متغيرات البيئة في إعدادات التطبيق
3. ادفع الكود:
```bash
git push heroku main
```

### Railway
1. ربط المشروع مع Railway
2. إضافة متغيرات البيئة
3. النشر التلقائي

### DigitalOcean/VPS
```bash
# تثبيت المتطلبات
sudo apt update
sudo apt install python3 python3-pip

# تشغيل البوت
nohup python3 main.py &
```

## API Endpoints

- `GET /` - الصفحة الرئيسية ومعلومات البوت
- `GET /api/products` - قائمة المنتجات
- `GET /api/shipping` - معلومات التوصيل
- `POST /whatsapp` - webhook لرسائل WhatsApp
- `GET /health` - فحص صحة الخدمة

## الأوامر المدعومة

### أوامر عامة
- `مرحبا` / `السلام` - رسالة الترحيب
- `منتجات` / `كتالوج` - عرض المنتجات
- `طلب + رقم` - طلب منتج (مثال: طلب 1)
- `توصيل` / `شحن` - معلومات التوصيل
- `مساعدة` - عرض جميع الأوامر

### أوامر Telegram الإضافية
- `/start` - بدء المحادثة
- `/help` - المساعدة
- `/products` - المنتجات
- `/shipping` - التوصيل

## التخصيص

### إضافة منتجات جديدة
عدّل قائمة `PRODUCTS` في ملف `config.py`:

```python
PRODUCTS = [
    {'id': 1, 'name': 'منتج جديد', 'price': 100},
    # أضف المزيد...
]
```

### تخصيص الرسائل
عدّل الرسائل في ملف `utils/message_handler.py`

### إضافة منصات جديدة
أنشئ ملف جديد في مجلد `platforms/` واتبع نفس النمط

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إرسال Pull Request

## الدعم

للدعم والاستفسارات:
- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.
