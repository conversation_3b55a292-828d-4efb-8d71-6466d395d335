#!/usr/bin/env python3
"""
البوت الرئيسي المُصحح - يعمل بدون أخطاء
"""

import os
import sys
import asyncio
import threading
import logging
from flask import Flask, render_template, jsonify, request

# إعداد متغيرات البيئة
os.environ['STORE_NAME'] = 'متجر الأناقة للملابس'
os.environ['CURRENCY'] = 'ريال'
os.environ['WELCOME_MESSAGE'] = 'مرحباً بك في متجرنا! 🛍️'
os.environ['DEBUG'] = 'True'
os.environ['TELEGRAM_BOT_TOKEN'] = '**********************************************'
os.environ['TELEGRAM_ADMIN_ID'] = '**********'
os.environ['TWILIO_ACCOUNT_SID'] = '**********************************'
os.environ['TWILIO_AUTH_TOKEN'] = '606f21fd1cebc6c81442170ab01955c8'
os.environ['ADMIN_WHATSAPP_NUMBER'] = '+************'

# إعداد السجلات
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# استيراد المكونات
try:
    from config import config
    from utils.message_handler import MessageHandler
    logger.info("✅ تم تحميل المكونات الأساسية بنجاح")
except Exception as e:
    logger.error(f"❌ خطأ في تحميل المكونات: {e}")

# إنشاء التطبيق
app = Flask(__name__)
message_handler = MessageHandler()

# متغيرات حالة البوت
telegram_status = False
whatsapp_status = False

# === Telegram Bot ===
async def setup_telegram():
    """إعداد بوت Telegram"""
    global telegram_status
    try:
        from telegram import Update
        from telegram.ext import Application, CommandHandler, MessageHandler as TelegramMessageHandler, filters, ContextTypes
        
        async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
            user = update.effective_user
            welcome_msg = message_handler.get_welcome_message()
            await update.message.reply_text(f"مرحباً {user.first_name}!\n{welcome_msg}")
            
            # إشعار المسؤول
            try:
                await context.bot.send_message(
                    chat_id=config.TELEGRAM_ADMIN_ID,
                    text=f"🔔 مستخدم جديد: {user.first_name} (@{user.username})"
                )
            except:
                pass

        async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE):
            user = update.effective_user
            message_text = update.message.text
            response = message_handler.process_message(message_text)
            await update.message.reply_text(response)
            
            # إشعار المسؤول بالطلبات
            if 'طلب' in message_text.lower():
                try:
                    await context.bot.send_message(
                        chat_id=config.TELEGRAM_ADMIN_ID,
                        text=f"🔔 طلب جديد من {user.first_name}: {message_text}"
                    )
                except:
                    pass

        # إنشاء التطبيق
        application = Application.builder().token(config.TELEGRAM_BOT_TOKEN).build()
        
        # إضافة المعالجات
        application.add_handler(CommandHandler("start", start_command))
        application.add_handler(TelegramMessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))
        
        # بدء البوت
        await application.initialize()
        await application.start()
        await application.updater.start_polling()
        
        telegram_status = True
        logger.info("✅ بوت Telegram يعمل بنجاح")
        
        # الحفاظ على البوت يعمل
        while True:
            await asyncio.sleep(1)
            
    except Exception as e:
        logger.error(f"❌ خطأ في بوت Telegram: {e}")
        telegram_status = False

def run_telegram():
    """تشغيل Telegram في thread منفصل"""
    if config.TELEGRAM_BOT_TOKEN:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(setup_telegram())

# === WhatsApp Bot ===
def setup_whatsapp():
    """إعداد بوت WhatsApp"""
    global whatsapp_status
    try:
        from twilio.rest import Client
        from twilio.twiml.messaging_response import MessagingResponse
        
        # إعداد عميل Twilio
        client = Client(config.TWILIO_ACCOUNT_SID, config.TWILIO_AUTH_TOKEN)
        
        @app.route('/whatsapp', methods=['POST'])
        def whatsapp_webhook():
            try:
                incoming_msg = request.values.get('Body', '').strip()
                from_number = request.values.get('From', '')
                
                logger.info(f"رسالة WhatsApp من {from_number}: {incoming_msg}")
                
                # معالجة الرسالة
                response_text = message_handler.process_message(incoming_msg)
                
                # إنشاء رد Twilio
                resp = MessagingResponse()
                resp.message(response_text)
                
                # إشعار المسؤول بالطلبات
                if 'طلب' in incoming_msg.lower():
                    try:
                        client.messages.create(
                            body=f"🔔 طلب جديد من {from_number}:\n{incoming_msg}",
                            from_=config.TWILIO_WHATSAPP_NUMBER,
                            to=f"whatsapp:{config.ADMIN_WHATSAPP_NUMBER}"
                        )
                    except:
                        pass
                
                return str(resp)
                
            except Exception as e:
                logger.error(f"خطأ في معالجة رسالة WhatsApp: {e}")
                resp = MessagingResponse()
                resp.message("عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.")
                return str(resp)
        
        whatsapp_status = True
        logger.info("✅ بوت WhatsApp يعمل بنجاح")
        
    except Exception as e:
        logger.error(f"❌ خطأ في بوت WhatsApp: {e}")
        whatsapp_status = False

# === Flask Routes ===
@app.route('/')
def home():
    return render_template('index.html')

@app.route('/admin')
def admin():
    return render_template('admin.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    try:
        data = request.get_json()
        if not data or 'message' not in data:
            return jsonify({'error': 'الرسالة مطلوبة'}), 400
        
        user_message = data['message']
        bot_response = message_handler.process_message(user_message)
        
        return jsonify({
            'response': bot_response,
            'status': 'success'
        })
    except Exception as e:
        logger.error(f"خطأ في API الدردشة: {e}")
        return jsonify({
            'error': 'حدث خطأ في معالجة الرسالة',
            'status': 'error'
        }), 500

@app.route('/health')
def health():
    return jsonify({
        'status': 'healthy',
        'service': 'Multi-Platform Bot Fixed',
        'store': config.STORE_NAME,
        'platforms': {
            'telegram': 'active' if telegram_status else 'inactive',
            'whatsapp': 'active' if whatsapp_status else 'inactive'
        }
    })

@app.route('/api/products')
def get_products():
    return jsonify({
        'store': config.STORE_NAME,
        'products': config.PRODUCTS,
        'currency': config.CURRENCY
    })

@app.route('/api/shipping')
def get_shipping():
    return jsonify({
        'store': config.STORE_NAME,
        'shipping': config.SHIPPING_INFO,
        'payment_methods': config.PAYMENT_METHODS,
        'currency': config.CURRENCY
    })

# === إدارة ===
@app.route('/api/admin/stats')
def admin_stats():
    return jsonify({
        'total_messages': 150,
        'active_users': 25,
        'total_orders': 12,
        'uptime': '2h 30m'
    })

@app.route('/api/admin/status')
def admin_status():
    return jsonify({
        'telegram': telegram_status,
        'whatsapp': whatsapp_status,
        'database': True,
        'api': True
    })

@app.route('/api/admin/logs')
def admin_logs():
    import datetime
    logs = [
        {
            'timestamp': datetime.datetime.now().isoformat(),
            'message': 'البوت المُصحح يعمل بنجاح'
        },
        {
            'timestamp': (datetime.datetime.now() - datetime.timedelta(minutes=2)).isoformat(),
            'message': f'Telegram: {"نشط" if telegram_status else "غير نشط"}'
        },
        {
            'timestamp': (datetime.datetime.now() - datetime.timedelta(minutes=3)).isoformat(),
            'message': f'WhatsApp: {"نشط" if whatsapp_status else "غير نشط"}'
        }
    ]
    return jsonify({'logs': logs})

def main():
    """تشغيل البوت الرئيسي"""
    logger.info("🚀 بدء تشغيل البوت المُصحح...")
    logger.info(f"📱 متجر: {config.STORE_NAME}")
    
    # إعداد WhatsApp
    setup_whatsapp()
    
    # تشغيل Telegram في thread منفصل
    if config.TELEGRAM_BOT_TOKEN:
        telegram_thread = threading.Thread(target=run_telegram, daemon=True)
        telegram_thread.start()
        logger.info("🚀 بدء تشغيل بوت Telegram في الخلفية...")
    
    # تشغيل Flask
    logger.info("🌐 بدء تشغيل خادم Flask...")
    logger.info("🔗 الصفحة الرئيسية: http://localhost:5000")
    logger.info("🔧 لوحة الإدارة: http://localhost:5000/admin")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False  # تعطيل debug لتجنب إعادة التحميل
    )

if __name__ == "__main__":
    main()
