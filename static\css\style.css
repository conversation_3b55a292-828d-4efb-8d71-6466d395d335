/* تحسينات إضافية للواجهة */

/* تحسين الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تأثيرات الحركة */
.message {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين الأزرار السريعة */
.quick-btn {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.quick-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* تحسين مؤشر الكتابة */
.typing-indicator {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

/* تحسين شريط التمرير */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* تحسين الرسائل */
.message-bubble {
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.message-bubble:hover {
    box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

/* تحسين الحالة */
.status-indicator {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .chat-container {
        margin: 10px;
        border-radius: 10px;
    }
    
    .quick-btn {
        font-size: 12px;
        padding: 5px 10px;
        margin: 2px;
    }
    
    .message-bubble {
        max-width: 85%;
        font-size: 14px;
    }
    
    .chat-header h2 {
        font-size: 1.5rem;
    }
}

/* تحسين الألوان للوضع الليلي */
@media (prefers-color-scheme: dark) {
    .chat-messages {
        background: #2c3e50;
    }
    
    .message.bot .message-bubble {
        background: #34495e;
        color: #ecf0f1;
    }
    
    .card {
        background: #34495e;
        color: #ecf0f1;
    }
}

/* تأثيرات خاصة */
.chat-header {
    position: relative;
    overflow: hidden;
}

.chat-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* تحسين التركيز */
.form-control:focus {
    border-color: #25D366;
    box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
}

/* تحسين الأيقونات */
.fas, .fab {
    margin-left: 5px;
}

/* تحسين الكروت */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}
