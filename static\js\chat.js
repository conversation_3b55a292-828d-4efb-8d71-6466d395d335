// مميزات متقدمة للدردشة

class ChatBot {
    constructor() {
        this.isTyping = false;
        this.messageHistory = [];
        this.currentSession = this.generateSessionId();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadChatHistory();
        this.startHeartbeat();
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    setupEventListeners() {
        // التعامل مع الضغط على Enter
        document.getElementById('messageInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // حفظ الرسائل عند إغلاق الصفحة
        window.addEventListener('beforeunload', () => {
            this.saveChatHistory();
        });

        // التعامل مع تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.scrollToBottom();
        });
    }

    async sendMessage() {
        const input = document.getElementById('messageInput');
        const message = input.value.trim();
        
        if (!message || this.isTyping) return;
        
        // إضافة الرسالة للتاريخ
        this.messageHistory.push({
            type: 'user',
            message: message,
            timestamp: new Date().toISOString()
        });

        // إضافة رسالة المستخدم للواجهة
        this.addMessage(message, 'user');
        input.value = '';
        
        // إظهار مؤشر الكتابة
        this.showTyping();
        
        try {
            const response = await this.callBotAPI(message);
            
            // محاكاة وقت الاستجابة
            const delay = Math.min(1000 + message.length * 50, 3000);
            
            setTimeout(() => {
                this.hideTyping();
                this.addMessage(response.response, 'bot');
                
                // إضافة الرد للتاريخ
                this.messageHistory.push({
                    type: 'bot',
                    message: response.response,
                    timestamp: new Date().toISOString()
                });
                
                this.saveChatHistory();
            }, delay);
            
        } catch (error) {
            setTimeout(() => {
                this.hideTyping();
                this.addMessage('عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'bot');
                this.updateStatus(false);
            }, 1000);
        }
    }

    async callBotAPI(message) {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Session-ID': this.currentSession
            },
            body: JSON.stringify({ 
                message: message,
                session_id: this.currentSession,
                timestamp: new Date().toISOString()
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        return await response.json();
    }

    addMessage(text, sender) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        
        const bubbleDiv = document.createElement('div');
        bubbleDiv.className = 'message-bubble';
        
        // إضافة الوقت
        const timestamp = new Date().toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
        
        if (sender === 'bot') {
            bubbleDiv.innerHTML = `
                <div class="message-content">${this.formatBotMessage(text)}</div>
                <div class="message-time" style="font-size: 11px; color: #666; margin-top: 5px;">
                    ${timestamp}
                </div>
            `;
        } else {
            bubbleDiv.innerHTML = `
                <div class="message-content">${this.escapeHtml(text)}</div>
                <div class="message-time" style="font-size: 11px; color: rgba(255,255,255,0.7); margin-top: 5px;">
                    ${timestamp} <i class="fas fa-check" style="margin-right: 3px;"></i>
                </div>
            `;
        }
        
        messageDiv.appendChild(bubbleDiv);
        messagesContainer.appendChild(messageDiv);
        
        this.scrollToBottom();
    }

    formatBotMessage(text) {
        // تحويل النص إلى HTML مع الحفاظ على التنسيق
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>')
            .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showTyping() {
        this.isTyping = true;
        document.getElementById('typingIndicator').style.display = 'block';
        this.scrollToBottom();
    }

    hideTyping() {
        this.isTyping = false;
        document.getElementById('typingIndicator').style.display = 'none';
    }

    scrollToBottom() {
        const container = document.getElementById('chatMessages');
        container.scrollTop = container.scrollHeight;
    }

    updateStatus(online) {
        const indicator = document.getElementById('statusIndicator');
        if (online) {
            indicator.className = 'status-indicator status-online';
            indicator.innerHTML = '<i class="fas fa-circle"></i> متصل';
        } else {
            indicator.className = 'status-indicator status-offline';
            indicator.innerHTML = '<i class="fas fa-circle"></i> غير متصل';
        }
    }

    async checkServerStatus() {
        try {
            const response = await fetch('/health');
            this.updateStatus(response.ok);
            return response.ok;
        } catch (error) {
            this.updateStatus(false);
            return false;
        }
    }

    startHeartbeat() {
        // فحص الحالة كل 30 ثانية
        setInterval(() => {
            this.checkServerStatus();
        }, 30000);
        
        // فحص أولي
        this.checkServerStatus();
    }

    saveChatHistory() {
        try {
            localStorage.setItem('chatHistory', JSON.stringify(this.messageHistory));
            localStorage.setItem('sessionId', this.currentSession);
        } catch (error) {
            console.warn('لا يمكن حفظ تاريخ المحادثة:', error);
        }
    }

    loadChatHistory() {
        try {
            const savedHistory = localStorage.getItem('chatHistory');
            const savedSession = localStorage.getItem('sessionId');
            
            if (savedHistory) {
                this.messageHistory = JSON.parse(savedHistory);
                
                // عرض آخر 10 رسائل
                const recentMessages = this.messageHistory.slice(-10);
                recentMessages.forEach(msg => {
                    if (msg.type !== 'bot' || msg.message !== 'مرحباً بك في متجر الأناقة للملابس! 👋') {
                        this.addMessage(msg.message, msg.type);
                    }
                });
            }
            
            if (savedSession) {
                this.currentSession = savedSession;
            }
        } catch (error) {
            console.warn('لا يمكن تحميل تاريخ المحادثة:', error);
        }
    }

    clearHistory() {
        this.messageHistory = [];
        localStorage.removeItem('chatHistory');
        document.getElementById('chatMessages').innerHTML = `
            <div class="message bot">
                <div class="message-bubble">
                    <strong>🤖 بوت متجر الأناقة</strong><br>
                    مرحباً بك في متجر الأناقة للملابس! 👋<br><br>
                    يمكنني مساعدتك في:<br>
                    🛍️ عرض المنتجات<br>
                    📏 معرفة المقاسات<br>
                    🚚 معلومات التوصيل<br>
                    💰 الأسعار والعروض<br><br>
                    اكتب "منتجات" لعرض أحدث المنتجات
                </div>
            </div>
        `;
    }
}

// تهيئة البوت عند تحميل الصفحة
let chatBot;

document.addEventListener('DOMContentLoaded', function() {
    chatBot = new ChatBot();
    
    // إضافة زر مسح التاريخ
    const clearBtn = document.createElement('button');
    clearBtn.className = 'btn btn-outline-danger btn-sm';
    clearBtn.innerHTML = '<i class="fas fa-trash"></i> مسح المحادثة';
    clearBtn.onclick = () => {
        if (confirm('هل تريد مسح تاريخ المحادثة؟')) {
            chatBot.clearHistory();
        }
    };
    
    document.querySelector('.quick-buttons').appendChild(clearBtn);
});

// دوال عامة للاستخدام في HTML
function sendQuickMessage(message) {
    document.getElementById('messageInput').value = message;
    chatBot.sendMessage();
}

function sendMessage() {
    chatBot.sendMessage();
}

function handleKeyPress(event) {
    if (event.key === 'Enter') {
        chatBot.sendMessage();
    }
}
